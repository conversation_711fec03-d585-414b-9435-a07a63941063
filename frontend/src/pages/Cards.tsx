import React, { useState, useEffect, useCallback } from "react";

import { Layout } from "@/components/layout/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge, badgeVariants } from "@/components/ui/badge";
import { Plus, Search, Filter, Eye, Edit, Trash2, CreditCard, Loader2, DollarSign, User, Image, AlertCircle, Calendar, Building2, X } from "lucide-react";
import { CardEditForm } from "@/components/cards/CardEditForm";
import { CardDetailView } from "@/components/cards/CardDetailView";
import { CardCreateForm } from "@/components/cards/CardCreateForm";
import { CustomerForm } from "@/components/customers/CustomerForm";
import { CustomerDetail } from "@/components/customers/CustomerDetail";
import { toast } from "sonner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CustomerCombobox } from "@/components/customers/CustomerCombobox";
import { getCustomerList, deleteCustomer } from "@/services/customerService";
import { getCardList, deleteCard, isHoldingCard } from "@/services/cardService";
import { Customer as CustomerType, calculateDaysUntilDue } from "@/models/customer";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import apiClient from "@/lib/apiClient";
import { formatCurrency } from "@/lib/utils";
import { formatDateTimeVN } from "@/utils/date-format";

// Component con để hiển thị mỗi card
interface CardRowProps {
  card: any;
  handleEditCard: (card: any) => void;
  handleDeleteCard: (cardId: number, cardNumber: string) => void;
  getPaymentWarning: (card: any) => Promise<string>;
  isHoldingCard: (card: any) => boolean;
  formatCurrency: (amount: number | string | undefined) => string;
  CardAttachmentIndicator: React.FC<{ cardId: number }>;
}

const CardRow = ({ card, handleEditCard, handleDeleteCard, getPaymentWarning, isHoldingCard, formatCurrency, CardAttachmentIndicator, onRowClick }: CardRowProps & { onRowClick: (card: any) => void }) => {
  const [paymentWarning, setPaymentWarning] = useState("");

  // Calculate payment warnings
  useEffect(() => {
    const calculateWarnings = async () => {
      // Payment warning
      console.log(`[DEBUG] Tính toán cảnh báo thanh toán cho thẻ ${card.id} (${card.card_number})`);
      console.log(`[DEBUG] Thông tin thẻ:`, {
        id: card.id,
        card_number: card.card_number,
        payment_date: card.payment_date,
        is_holding_card: card.is_holding_card,
        isHoldingCard: isHoldingCard(card)
      });

      const warning = await getPaymentWarning(card);
      console.log(`[DEBUG] Kết quả cảnh báo: "${warning}"`);
      setPaymentWarning(warning);
    };

    calculateWarnings();
  }, [card, getPaymentWarning]);

  return (
    <TableRow
      className="group cursor-pointer hover:bg-muted/50"
      onClick={() => onRowClick(card)}
    >

      <TableCell>
        <div className="flex flex-col">
          <div className="flex items-center gap-1">
            <span className="font-medium">{card.card_number}</span>
            {card.bank_name && (
              <span className="text-xs bg-primary/10 text-primary px-1.5 py-0.5 rounded">
                {card.bank_name}
              </span>
            )}
            <CardAttachmentIndicator cardId={card.id} />
          </div>
          <div className="flex items-center gap-2 mt-1 text-xs">
            {isHoldingCard(card) ? (
              <Badge variant="outline" className="bg-green-500/10 text-green-600 border-green-200 text-xs px-1 py-0 h-5">
                Giữ
              </Badge>
            ) : (
              <Badge variant="outline" className="bg-gray-500/10 text-gray-600 border-gray-200 text-xs px-1 py-0 h-5">
                KG
              </Badge>
            )}
            <span>
              HM: {card.credit_limit ? formatCurrency(card.credit_limit) : '-'}
            </span>
          </div>
        </div>
      </TableCell>
      <TableCell>
        <div className="flex flex-col">
          <span className="font-medium">{card.customer_name || card.Customer?.name}</span>
          <span className="text-xs text-muted-foreground">
            {card.customer_phone || card.Customer?.phone || 'Chưa có SĐT'}
          </span>
        </div>
      </TableCell>

      <TableCell>
        <div className="flex flex-col">
          <div className="flex items-center text-xs">
            <Badge variant="outline" className="mr-1">
              Sao kê
            </Badge>
            {card.statement_date ? `Ngày ${card.statement_date}` : '-'}
          </div>
          <div className="flex items-center text-xs mt-1">
            <Badge variant="outline" className={`mr-1 ${
              paymentWarning.includes("Gấp") || paymentWarning.includes("hôm nay") ? 'bg-red-100 text-red-800 border-red-200' :
              paymentWarning.includes("Sắp") ? 'bg-amber-100 text-amber-800 border-amber-200' :
              paymentWarning.includes("Quá hạn") ? 'bg-red-100 text-red-800 border-red-200' :
              paymentWarning.includes("Đã Đáo") ? 'bg-green-100 text-green-800 border-green-200' : ''
            }`}>
              Đến hạn
            </Badge>
            {card.payment_date ? `Ngày ${card.payment_date}` : '-'}
            {paymentWarning && (
              <Badge variant="outline" className={`ml-2 ${
                paymentWarning.includes("Gấp") || paymentWarning.includes("hôm nay") ? 'bg-red-100 text-red-800 border-red-200' :
                paymentWarning.includes("Sắp") ? 'bg-amber-100 text-amber-800 border-amber-200' :
                paymentWarning.includes("Quá hạn") ? 'bg-red-100 text-red-800 border-red-200' :
                paymentWarning.includes("Đã Đáo") ? 'bg-green-100 text-green-800 border-green-200' : ''
              }`}>
                {paymentWarning}
              </Badge>
            )}
          </div>
        </div>
      </TableCell>
      <TableCell className="text-right">
        <div className="flex justify-end opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation(); // Ngăn chặn sự kiện click lan truyền lên dòng
              handleEditCard(card);
            }}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={(e) => {
              e.stopPropagation(); // Ngăn chặn sự kiện click lan truyền lên dòng
              handleDeleteCard(card.id, card.card_number);
            }}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );
};

const Cards = () => {
  // State for cards
  const [cards, setCards] = useState<any[]>([]);
  const [filteredCards, setFilteredCards] = useState<any[]>([]);
  const [customers, setCustomers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [cardFilters, setCardFilters] = useState({
    isHoldingCard: "all",
    customerId: "all"
  });
  const [editingCard, setEditingCard] = useState<any>(null);
  const [viewingCard, setViewingCard] = useState<any>(null);
  const [showViewCardModal, setShowViewCardModal] = useState(false);
  const [showEditCardModal, setShowEditCardModal] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
  const [showCreateCardModal, setShowCreateCardModal] = useState(false);
  const [showCustomerDetail, setShowCustomerDetail] = useState(false);
  const [showCustomerFilterModal, setShowCustomerFilterModal] = useState(false);
  const [view, setView] = useState<"list" | "detail" | "edit">("list");

  // Fetch cards and customers
  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      // Fetch cards
      const cardsResponse = await getCardList();
      if (cardsResponse.success) {
        setCards(cardsResponse.data || []);
        setFilteredCards(cardsResponse.data || []);
        setTotalItems(cardsResponse.data?.length || 0);
        setTotalPages(Math.ceil((cardsResponse.data?.length || 0) / itemsPerPage));
      }

      // Fetch customers
      const customersResponse = await getCustomerList(1, 1000); // Lấy tất cả khách hàng
      if (customersResponse.success) {
        setCustomers(customersResponse.data || []);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Không thể tải dữ liệu. Vui lòng thử lại sau.");
    } finally {
      setLoading(false);
    }
  }, [itemsPerPage]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  // Filter cards based on search term and filters
  useEffect(() => {
    let result = [...cards];

    // Apply search filter
    if (searchTerm) {
      const lowerCaseSearchTerm = searchTerm.toLowerCase();
      result = result.filter(
        card =>
          card.card_number?.toLowerCase().includes(lowerCaseSearchTerm) ||
          card.Customer?.name?.toLowerCase().includes(lowerCaseSearchTerm) ||
          card.bank_name?.toLowerCase().includes(lowerCaseSearchTerm)
      );
    }

    // Apply holding card filter
    if (cardFilters.isHoldingCard !== "all") {
      const isHolding = cardFilters.isHoldingCard === "yes";
      result = result.filter(card => isHoldingCard(card) === isHolding);
    }

    // Apply customer filter
    if (cardFilters.customerId !== "all") {
      result = result.filter(card => card.customer_id.toString() === cardFilters.customerId);
    }

    // Sắp xếp thẻ: thẻ đang giữ lên trên, sau đó sắp xếp theo ngày thanh toán
    result.sort((a, b) => {
      // Ưu tiên thẻ đang giữ
      const aIsHolding = isHoldingCard(a) ? 1 : 0;
      const bIsHolding = isHoldingCard(b) ? 1 : 0;

      if (aIsHolding !== bIsHolding) {
        return bIsHolding - aIsHolding; // Thẻ đang giữ lên trên
      }

      // Nếu cùng trạng thái giữ thẻ, kiểm tra ngày thanh toán
      const aHasPaymentDate = a.payment_date ? true : false;
      const bHasPaymentDate = b.payment_date ? true : false;

      // Thẻ không có ngày thanh toán xuống dưới cùng
      if (aHasPaymentDate !== bHasPaymentDate) {
        return aHasPaymentDate ? -1 : 1; // Thẻ có ngày thanh toán lên trên
      }

      // Nếu cả hai thẻ đều có ngày thanh toán, sắp xếp theo số ngày còn lại
      if (aHasPaymentDate && bHasPaymentDate) {
        const aDays = calculateDaysUntilDue(a.payment_date);
        const bDays = calculateDaysUntilDue(b.payment_date);
        return aDays - bDays; // Sắp xếp tăng dần theo số ngày còn lại
      }

      // Nếu cả hai thẻ đều không có ngày thanh toán, sắp xếp theo ID (mới nhất lên trên)
      return b.id - a.id;
    });

    setFilteredCards(result);
    setTotalItems(result.length);
    setTotalPages(Math.ceil(result.length / itemsPerPage));
    setCurrentPage(1); // Reset to first page when filters change
  }, [cards, searchTerm, cardFilters, itemsPerPage]);

  // Pagination
  const paginatedCards = filteredCards.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Handle card edit
  const handleEditCard = (card: any) => {
    console.log("Editing card:", card);
    setEditingCard(card);
    setSelectedCustomerId(card.customer_id.toString());
    setShowEditCardModal(true);
  };

  // Handle card view
  const handleViewCard = (card: any) => {
    console.log("Viewing card:", card);
    setViewingCard(card);
    setShowViewCardModal(true);
  };

  // Handle card edit success
  const handleEditCardSuccess = () => {
    setShowEditCardModal(false);
    setEditingCard(null);
    fetchData();
    toast.success("Thẻ đã được cập nhật thành công");
  };

  // Check if card has orders
  const checkCardHasOrders = async (cardId: number) => {
    try {
      const response = await apiClient.get(`/api/cards/${cardId}/has-orders`);
      return response.data;
    } catch (error) {
      console.error('Error checking card orders:', error);
      return {
        hasOrders: false,
        orderCount: 0
      };
    }
  };

  // Handle card delete
  const handleDeleteCard = async (cardId: number, cardNumber: string) => {
    try {
      // Kiểm tra xem thẻ có liên kết với đơn hàng không
      const { hasOrders, orderCount } = await checkCardHasOrders(cardId);

      if (hasOrders) {
        toast.error(`Thẻ ${cardNumber} có liên kết với ${orderCount} đơn hàng nên không thể xóa. Vui lòng liên hệ admin để xử lý.`);
        return;
      }

      // Nếu không có liên kết, hiển thị xác nhận xóa
      if (window.confirm(`Bạn có chắc chắn muốn xóa thẻ ${cardNumber} không?`)) {
        const response = await apiClient.delete(`/api/cards/${cardId}`);

        if (response.data.success) {
          toast.success('Đã xóa thẻ thành công');
          // Refresh danh sách thẻ
          fetchData();
        } else {
          toast.error(response.data.message || 'Không thể xóa thẻ');
        }
      }
    } catch (error) {
      console.error('Error deleting card:', error);
      toast.error('Đã xảy ra lỗi khi xóa thẻ');
    }
  };

  // Check if card has recent Dao order
  const hasRecentDaoOrder = async (card: any) => {
    if (!card || !card.id) return false;

    // Chỉ kiểm tra đơn hàng Đáo cho thẻ đang giữ
    if (!isHoldingCard(card)) return false;

    const cardId = card.id;
    const today = new Date();

    // Lấy ngày đầu tiên của tháng hiện tại
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // Lấy ngày cuối cùng của tháng hiện tại
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    // Đảm bảo chỉ lấy đơn hàng trong tháng hiện tại
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    try {
      console.log(`[DEBUG] Kiểm tra đơn hàng Đáo cho thẻ ${cardId} trong tháng ${currentMonth + 1}/${currentYear}`);
      console.log(`[DEBUG] Khoảng thời gian: từ ${firstDayOfMonth.toISOString().split('T')[0]} đến ${lastDayOfMonth.toISOString().split('T')[0]}`);

      const response = await apiClient.get(`/api/cards/${cardId}/orders`, {
        params: {
          start_date: firstDayOfMonth.toISOString().split('T')[0],
          end_date: lastDayOfMonth.toISOString().split('T')[0],
          order_type: 'Đáo'
        }
      });

      if (response.data && response.data.success && response.data.data) {
        const daoOrders = response.data.data;
        console.log(`[DEBUG] Tìm thấy ${daoOrders.length} đơn hàng Đáo cho thẻ ${cardId}:`, daoOrders);

        // In ra tất cả các đơn hàng để debug
        daoOrders.forEach((order: any, index: number) => {
          console.log(`[DEBUG] Đơn hàng #${index + 1} (ID: ${order.id}):`, order);
        });

        // Lọc chỉ lấy đơn hàng trong tháng hiện tại và đã hoàn thành
        const completedDaoOrders = daoOrders.filter((order: any) => {
          // Kiểm tra trạng thái hoàn thành
          const isCompleted = order.status === 'completed' || order.status === 'Hoàn thành';

          // Kiểm tra thời gian tạo đơn hàng
          let isCurrentMonth = false;
          let orderDate: Date | null = null;
          let dateSource = '';

          if (order.created_at) {
            try {
              orderDate = new Date(order.created_at);
              // Kiểm tra xem ngày có hợp lệ không
              if (!isNaN(orderDate.getTime())) {
                dateSource = 'created_at';
                isCurrentMonth =
                  orderDate.getMonth() === currentMonth &&
                  orderDate.getFullYear() === currentYear;
              } else {
                console.log(`[DEBUG] Đơn hàng ${order.id}: created_at không hợp lệ: ${order.created_at}`);
                orderDate = null;
              }
            } catch (error) {
              console.log(`[DEBUG] Đơn hàng ${order.id}: Lỗi khi parse created_at: ${order.created_at}`, error);
              orderDate = null;
            }
          } else if (order.date) {
            // Nếu không có created_at, thử dùng trường date
            try {
              orderDate = new Date(order.date);
              if (!isNaN(orderDate.getTime())) {
                dateSource = 'date';
                isCurrentMonth =
                  orderDate.getMonth() === currentMonth &&
                  orderDate.getFullYear() === currentYear;
              } else {
                console.log(`[DEBUG] Đơn hàng ${order.id}: date không hợp lệ: ${order.date}`);
                orderDate = null;
              }
            } catch (error) {
              console.log(`[DEBUG] Đơn hàng ${order.id}: Lỗi khi parse date: ${order.date}`, error);
              orderDate = null;
            }
          } else if (order.order_date) {
            // Thử dùng trường order_date nếu có
            try {
              orderDate = new Date(order.order_date);
              if (!isNaN(orderDate.getTime())) {
                dateSource = 'order_date';
                isCurrentMonth =
                  orderDate.getMonth() === currentMonth &&
                  orderDate.getFullYear() === currentYear;
              } else {
                console.log(`[DEBUG] Đơn hàng ${order.id}: order_date không hợp lệ: ${order.order_date}`);
                orderDate = null;
              }
            } catch (error) {
              console.log(`[DEBUG] Đơn hàng ${order.id}: Lỗi khi parse order_date: ${order.order_date}`, error);
              orderDate = null;
            }
          } else if (order.completed_at) {
            // Thử dùng trường completed_at nếu có
            try {
              orderDate = new Date(order.completed_at);
              if (!isNaN(orderDate.getTime())) {
                dateSource = 'completed_at';
                isCurrentMonth =
                  orderDate.getMonth() === currentMonth &&
                  orderDate.getFullYear() === currentYear;
              } else {
                console.log(`[DEBUG] Đơn hàng ${order.id}: completed_at không hợp lệ: ${order.completed_at}`);
                orderDate = null;
              }
            } catch (error) {
              console.log(`[DEBUG] Đơn hàng ${order.id}: Lỗi khi parse completed_at: ${order.completed_at}`, error);
              orderDate = null;
            }
          } else if (order.updated_at) {
            // Nếu không có các trường date khác, kiểm tra trường updated_at
            try {
              orderDate = new Date(order.updated_at);
              if (!isNaN(orderDate.getTime())) {
                dateSource = 'updated_at';
                isCurrentMonth =
                  orderDate.getMonth() === currentMonth &&
                  orderDate.getFullYear() === currentYear;
              } else {
                console.log(`[DEBUG] Đơn hàng ${order.id}: updated_at không hợp lệ: ${order.updated_at}`);
                orderDate = null;
              }
            } catch (error) {
              console.log(`[DEBUG] Đơn hàng ${order.id}: Lỗi khi parse updated_at: ${order.updated_at}`, error);
              orderDate = null;
            }
          } else {
            dateSource = 'none';
            // Nếu không có thông tin thời gian, giả định đơn hàng không phải của tháng hiện tại
            isCurrentMonth = false;
          }

          console.log(`[DEBUG] Đơn hàng ${order.id}: source=${dateSource}, date=${orderDate ? orderDate.toISOString() : 'N/A'}, isCurrentMonth=${isCurrentMonth}, isCompleted=${isCompleted}`);

          return isCompleted && isCurrentMonth;
        });

        console.log(`[DEBUG] Có ${completedDaoOrders.length} đơn hàng Đáo đã hoàn thành trong tháng ${currentMonth + 1}/${currentYear} cho thẻ ${cardId}`);
        return completedDaoOrders.length > 0;
      }

      return false;
    } catch (error) {
      console.error(`[ERROR] Lỗi khi kiểm tra đơn hàng Đáo cho thẻ ${cardId}:`, error);
      return false;
    }
  };

  // Function to generate warning message based on days remaining
  const getPaymentWarning = async (card: any) => {
    if (!card) return "";

    // Check if card is being held
    if (!isHoldingCard(card)) return "";

    try {
      // Nếu không có ngày thanh toán, trả về chuỗi rỗng
      if (!card.payment_date) {
        return "Chưa có ngày thanh toán";
      }

      // If card has "Đáo" orders in current month, return "Đã Đáo"
      const hasDaoOrder = await hasRecentDaoOrder(card);
      console.log(`[DEBUG] Thẻ ${card.id} có đơn hàng Đáo trong tháng: ${hasDaoOrder}`);

      if (hasDaoOrder) {
        return "Đã Đáo";
      }

      // Sử dụng hàm calculateDaysUntilDue từ models/customer
      const days = calculateDaysUntilDue(card.payment_date);

      // Nếu days là 9999, nghĩa là không có ngày thanh toán
      if (days === 9999) {
        return "Chưa có ngày thanh toán";
      } else if (days >= 1 && days <= 3) {
        return `Đến hạn ${days} ngày! Gấp`;
      } else if (days >= 4 && days <= 10) {
        return `Sắp đến hạn: còn ${days} ngày`;
      } else if (days > 10) {
        return `Còn ${days} ngày`;
      } else if (days === 0) {
        return "Đến hạn hôm nay!";
      } else if (days < 0) {
        return `Quá hạn ${Math.abs(days)} ngày!`;
      }

      return "";
    } catch (error) {
      console.error("Error calculating payment warning:", error);
      return "";
    }
  };

  // Card attachment indicator
  const CardAttachmentIndicator = ({ cardId }: { cardId: number }) => {
    const [hasAttachment, setHasAttachment] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(false);

    useEffect(() => {
      const checkAttachment = async () => {
        setIsLoading(true);
        setError(false);
        try {
          // Sử dụng đường dẫn API đúng
          const response = await apiClient.get(`/api/attachments/card/${cardId}`);
          if (response.data && response.data.success) {
            setHasAttachment(response.data.data && response.data.data.length > 0);
          }
        } catch (error) {
          console.error("Error checking card attachment:", error);
          setError(true);
        } finally {
          setIsLoading(false);
        }
      };

      checkAttachment();
    }, [cardId]);

    if (isLoading || error || !hasAttachment) return null;

    return (
      <span className="inline-flex items-center ml-1">
        <Image className="h-3 w-3 text-blue-600" />
      </span>
    );
  };

  // Handle card form success
  const handleCardFormSuccess = () => {
    setShowCreateCardModal(false);
    fetchData();
    toast.success("Thao tác thành công");
  };

  // Handle show create card modal
  const handleShowCreateCardModal = () => {
    setSelectedCustomerId(null);
    setShowCreateCardModal(true);
  };

  // Render modal chỉnh sửa thẻ
  const renderEditCardModal = () => {
    console.log("Rendering edit card modal with card:", editingCard);
    return (
      <Dialog
        open={showEditCardModal}
        onOpenChange={(open) => {
          console.log("[DEBUG] Card modal open state changed to:", open);
          setShowEditCardModal(open);
          if (!open) {
            // Khi đóng modal mà không phải do nút Cancel hoặc onSuccess
            console.log("[DEBUG] Card modal closed manually");
            setEditingCard(null); // Xóa thẻ đang chỉnh sửa khi đóng modal
          }
        }}
      >
        <DialogContent className="max-w-[800px] max-h-[90vh] overflow-y-auto" aria-labelledby="edit-card-title" aria-describedby="edit-card-description">
          <DialogHeader>
            <DialogTitle id="edit-card-title">Chỉnh sửa thẻ</DialogTitle>
            <DialogDescription id="edit-card-description">Cập nhật thông tin thẻ đã chọn</DialogDescription>
          </DialogHeader>
          {editingCard && (
            <CardEditForm
              customerId={editingCard?.customer_id?.toString()}
              cardId={editingCard?.id}
              onCancel={() => {
                setShowEditCardModal(false);
                setEditingCard(null);
              }}
              onSuccess={handleEditCardSuccess}
            />
          )}
        </DialogContent>
      </Dialog>
    );
  };

  // Render modal xem chi tiết thẻ
  const renderViewCardModal = () => {
    return (
      <Dialog
        open={showViewCardModal}
        onOpenChange={(open) => {
          setShowViewCardModal(open);
          if (!open) {
            setViewingCard(null); // Xóa thẻ đang xem khi đóng modal
          }
        }}
      >
        <DialogContent className="max-w-[800px] max-h-[90vh] overflow-y-auto" aria-labelledby="view-card-title" aria-describedby="view-card-description">
          {viewingCard && (
            <CardDetailView cardId={viewingCard?.id} />
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowViewCardModal(false)}
            >
              Đóng
            </Button>
            <Button
              onClick={() => {
                setShowViewCardModal(false);
                setTimeout(() => {
                  handleEditCard(viewingCard);
                }, 100); // Thêm độ trễ nhỏ để đảm bảo modal đầu tiên đã đóng
              }}
            >
              <Edit className="h-4 w-4 mr-2" />
              Chỉnh sửa
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  // Mobile Card View Component
  const MobileCardView = ({ card }: { card: any }) => {
    const [paymentWarning, setPaymentWarning] = useState("");

    useEffect(() => {
      const calculateWarnings = async () => {
        const warning = await getPaymentWarning(card);
        setPaymentWarning(warning);
      };
      calculateWarnings();
    }, [card]);

    return (
      <Card className="mb-2 shadow-sm cursor-pointer" onClick={() => handleViewCard(card)}>
        <CardContent className="p-3">
          {/* Header: Card Number + Bank Name + Actions */}
          <div className="flex justify-between items-center mb-2">
            <div className="flex items-center gap-2 flex-1 min-w-0 overflow-hidden">
              <span className="font-semibold text-sm truncate flex-shrink-0 min-w-0">{card.card_number}</span>
              {card.bank_name && (
                <Badge variant="secondary" className="text-xs font-medium flex-shrink-0">
                  {card.bank_name}
                </Badge>
              )}
              <CardAttachmentIndicator cardId={card.id} />
            </div>
            <div className="flex gap-1 ml-2 flex-shrink-0">
              <Button
                size="icon"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditCard(card);
                }}
                className="h-7 w-7"
                aria-label="Sửa"
              >
                <Edit className="h-3.5 w-3.5" />
              </Button>
              <Button
                size="icon"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteCard(card.id, card.card_number);
                }}
                className="text-destructive hover:text-destructive hover:bg-destructive/10 h-7 w-7"
                aria-label="Xóa"
              >
                <Trash2 className="h-3.5 w-3.5" />
              </Button>
            </div>
          </div>

          {/* Info Row: Status + Customer + Credit Limit */}
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center gap-2">
              {isHoldingCard(card) ? (
                <Badge variant="outline" className="bg-green-500/10 text-green-600 border-green-200 text-xs px-1.5 py-0.5">
                  Giữ
                </Badge>
              ) : (
                <Badge variant="outline" className="bg-gray-500/10 text-gray-600 border-gray-200 text-xs px-1.5 py-0.5">
                  KG
                </Badge>
              )}
              <span className="text-muted-foreground truncate">
                {card.customer_name || card.Customer?.name}
              </span>
            </div>
            <span className="text-muted-foreground ml-2 flex-shrink-0">
              HM: {card.credit_limit ? formatCurrency(card.credit_limit) : '-'}
            </span>
          </div>

          {/* Dates Row: Statement + Payment + Warning */}
          <div className="flex items-center justify-between text-xs mt-2 pt-2 border-t">
            <div className="flex items-center gap-3">
              <span className="text-muted-foreground">
                SK: {card.statement_date ? card.statement_date : '-'}
              </span>
              <span className="text-muted-foreground">
                TT: {card.payment_date ? card.payment_date : '-'}
              </span>
            </div>
            {paymentWarning && (
              <Badge variant="outline" className={`text-xs px-1.5 py-0.5 flex-shrink-0 ${
                paymentWarning.includes("Gấp") || paymentWarning.includes("hôm nay") ? 'bg-red-100 text-red-800 border-red-200' :
                paymentWarning.includes("Sắp") ? 'bg-amber-100 text-amber-800 border-amber-200' :
                paymentWarning.includes("Quá hạn") ? 'bg-red-100 text-red-800 border-red-200' :
                paymentWarning.includes("Đã Đáo") ? 'bg-green-100 text-green-800 border-green-200' : ''
              }`}>
                {paymentWarning}
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  // Render content based on view
  const renderContent = () => {
    return (
      <div className="space-y-6">
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Quản lý thẻ</h1>
            <p className="text-muted-foreground mt-1">
              Quản lý thông tin thẻ ngân hàng
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setShowFilterModal(true)} className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              <span className="hidden sm:inline">Lọc thẻ</span>
            </Button>
            <Button onClick={handleShowCreateCardModal}>
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Thêm mới</span>
              <span className="sm:hidden">Thêm</span>
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              <div>
                <CardTitle className="text-lg font-medium">Danh sách thẻ</CardTitle>
                <CardDescription className="mt-1">
                  Quản lý thông tin thẻ ngân hàng
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {totalItems} thẻ
                </Badge>
              </div>
            </div>

            <div className="flex flex-col gap-4 mt-4">
              {/* Search and Filter Controls */}
              <div className="flex flex-col gap-3">
                {/* Search Bar and Filter Button */}
                <div className="flex gap-3">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Tìm kiếm thẻ..."
                      className="pl-9 h-10"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFilterModal(true)}
                    className="flex items-center gap-2 px-3 h-10 flex-shrink-0"
                  >
                    <Filter className="h-4 w-4" />
                    <span className="hidden sm:inline">Bộ lọc</span>
                  </Button>
                </div>

                {/* Filter Status */}
                {(cardFilters.isHoldingCard !== "all" || cardFilters.customerId !== "all") && (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="py-1.5 px-3 text-xs">
                      Đang lọc
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 ml-2 -mr-1"
                        onClick={() => setCardFilters({ isHoldingCard: "all", customerId: "all" })}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {/* Loading State */}
            {loading && (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Đang tải dữ liệu...</span>
              </div>
            )}

            {/* Desktop Table View - Hidden on mobile */}
            {!loading && (
              <div className="hidden lg:block">
                <div className="rounded-lg border overflow-hidden">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Thông tin thẻ</TableHead>
                          <TableHead>Khách hàng</TableHead>
                          <TableHead>Ngày sao kê/đến hạn</TableHead>
                          <TableHead className="text-right">Thao tác</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {paginatedCards.map((card) => (
                          <CardRow
                            key={card.id}
                            card={card}
                            handleEditCard={handleEditCard}
                            handleDeleteCard={handleDeleteCard}
                            getPaymentWarning={getPaymentWarning}
                            isHoldingCard={isHoldingCard}
                            formatCurrency={formatCurrency}
                            CardAttachmentIndicator={CardAttachmentIndicator}
                            onRowClick={handleViewCard}
                          />
                        ))}

                        {paginatedCards.length === 0 && (
                          <TableRow>
                            <TableCell colSpan={4} className="h-24 text-center">
                              <div className="flex flex-col items-center gap-2">
                                <CreditCard className="h-6 w-6 text-muted-foreground" />
                                <p className="text-muted-foreground">Không có thẻ nào</p>
                              </div>
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>
            )}

            {/* Mobile Card View - Visible on mobile and tablet */}
            {!loading && (
              <div className="lg:hidden">
                {paginatedCards.length > 0 ? (
                  <div className="space-y-4">
                    {paginatedCards.map((card) => (
                      <MobileCardView key={card.id} card={card} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      Không có thẻ nào
                    </p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <Layout>
      <div className="container mx-auto py-6">
        {renderContent()}

        {/* Modal tạo thẻ mới */}
        <Dialog open={showCreateCardModal} onOpenChange={setShowCreateCardModal}>
          <DialogContent className="md:max-w-[800px] max-h-[90vh] overflow-y-auto" aria-labelledby="create-card-title" aria-describedby="create-card-description">
            <CardCreateForm
              onCancel={() => setShowCreateCardModal(false)}
              onSuccess={handleCardFormSuccess}
            />
          </DialogContent>
        </Dialog>

        {/* Modal chỉnh sửa thẻ */}
        {renderEditCardModal()}

        {/* Modal xem chi tiết thẻ */}
        {renderViewCardModal()}

        {/* Modal lọc thẻ */}
        <Dialog open={showFilterModal} onOpenChange={setShowFilterModal}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle>Lọc thẻ</DialogTitle>
              <DialogDescription>
                Chọn các tiêu chí để lọc danh sách thẻ
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-12 items-center gap-4">
                <div className="col-span-3 flex justify-end">
                  <Label htmlFor="holdingFilter" className="flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Trạng thái
                  </Label>
                </div>
                <div className="col-span-9">
                  <Select
                    value={cardFilters.isHoldingCard}
                    onValueChange={(value) => setCardFilters({...cardFilters, isHoldingCard: value})}
                  >
                    <SelectTrigger id="holdingFilter" className="w-full">
                      <SelectValue placeholder="Chọn trạng thái" />
                    </SelectTrigger>
                    <SelectContent side="bottom" align="start">
                      <SelectItem value="all">Tất cả thẻ</SelectItem>
                      <SelectItem value="yes">Đang giữ thẻ</SelectItem>
                      <SelectItem value="no">Không giữ thẻ</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-12 items-center gap-4">
                <div className="col-span-3 flex justify-end">
                  <Label htmlFor="customerFilter" className="flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Khách hàng
                  </Label>
                </div>
                <div className="col-span-9">
                  <div className="flex gap-2 items-center">
                    <CustomerCombobox
                      value={cardFilters.customerId !== "all" ? Number(cardFilters.customerId) : null}
                      onChange={(value) => setCardFilters({...cardFilters, customerId: value ? value.toString() : "all"})}
                      placeholder="Chọn khách hàng"
                    />
                    {cardFilters.customerId !== "all" && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setCardFilters({...cardFilters, customerId: "all"})}
                      >
                        Xóa
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter className="flex justify-between sm:justify-between">
              <Button
                onClick={() => {
                  setCardFilters({
                    isHoldingCard: "all",
                    customerId: "all"
                  });
                }}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                Xóa bộ lọc
              </Button>
              <Button
                onClick={() => setShowFilterModal(false)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                Áp dụng bộ lọc
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="mt-6">
            {/* Mobile Pagination - Simple */}
            <div className="flex md:hidden items-center justify-between">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="flex items-center gap-1"
              >
                <span>Trước</span>
              </Button>

              <span className="text-sm text-muted-foreground">
                Trang {currentPage} / {totalPages}
              </span>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="flex items-center gap-1"
              >
                <span>Sau</span>
              </Button>
            </div>

            {/* Desktop Pagination - Full */}
            <div className="hidden md:flex items-center justify-between">
              <div className="text-sm text-muted-foreground">
                Hiển thị {(currentPage - 1) * itemsPerPage + 1} - {Math.min(currentPage * itemsPerPage, totalItems)} trên tổng số {totalItems} thẻ
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Trước
                </Button>

                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Hiển thị 5 trang gần trang hiện tại
                  let pageToShow;
                  if (totalPages <= 5) {
                    pageToShow = i + 1;
                  } else if (currentPage <= 3) {
                    pageToShow = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageToShow = totalPages - 4 + i;
                  } else {
                    pageToShow = currentPage - 2 + i;
                  }

                  return (
                    <Button
                      key={i}
                      variant={currentPage === pageToShow ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(pageToShow)}
                    >
                      {pageToShow}
                    </Button>
                  );
                })}

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Sau
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );


};

export default Cards;
