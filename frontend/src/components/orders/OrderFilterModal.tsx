import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  Di<PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { CalendarIcon } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CustomerCombobox } from "@/components/customers/CustomerCombobox";

interface OrderFilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (filters: {
    customerName: string;
    customerId?: number;
    orderType: string;
    startDate: Date | undefined;
    endDate: Date | undefined;
  }) => void;
  initialFilters?: {
    customerName: string;
    customerId?: number;
    orderType: string;
    startDate: Date | undefined;
    endDate: Date | undefined;
  };
  orders: Array<{
    customerName: string;
    customerId?: number;
  }>;
}

const ORDER_TYPES_FILTER = ["Đáo", "Rút"];

export function OrderFilterModal({
  isOpen,
  onClose,
  onApply,
  initialFilters = {
    customerName: "all",
    customerId: undefined,
    orderType: "all",
    startDate: undefined,
    endDate: undefined,
  },
  orders,
}: OrderFilterModalProps) {
  const [customerName, setCustomerName] = useState(initialFilters.customerName);
  const [customerId, setCustomerId] = useState<number | undefined>(initialFilters.customerId);
  const [orderType, setOrderType] = useState(initialFilters.orderType);
  const [startDate, setStartDate] = useState<Date | undefined>(initialFilters.startDate);
  const [endDate, setEndDate] = useState<Date | undefined>(initialFilters.endDate);

  // Tạo danh sách khách hàng duy nhất từ orders
  // Tạo một mảng các cặp [customerName, customerId]
  const customerMap = orders.reduce((acc, order) => {
    if (order.customerName && !acc.has(order.customerName)) {
      acc.set(order.customerName, order.customerId);
    }
    return acc;
  }, new Map<string, number | undefined>());

  // Chuyển Map thành mảng và sắp xếp theo tên
  const uniqueCustomers = Array.from(customerMap.entries())
    .map(([name, id]) => ({ name, id }))
    .sort((a, b) => a.name.localeCompare(b.name));

  const handleApply = () => {
    // Đảm bảo rằng nếu có customerId, chúng ta sẽ sử dụng nó thay vì customerName
    // Điều này giúp tìm kiếm chính xác hơn
    onApply({
      customerName: customerId ? 'all' : customerName, // Chỉ sử dụng customerName khi không có customerId
      customerId,
      orderType,
      startDate,
      endDate,
    });
    onClose();
  };

  const handleReset = () => {
    // Chỉ đặt lại các giá trị mà không áp dụng bộ lọc ngay lập tức
    setCustomerName("all");
    setCustomerId(undefined);
    setOrderType("all");
    setStartDate(undefined);
    setEndDate(undefined);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Bộ lọc đơn hàng</DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="grid gap-2">
            <label htmlFor="customerName" className="text-sm font-medium">
              Khách hàng
            </label>
            <div className="flex gap-2 items-center">
              <CustomerCombobox
                value={customerId}
                onChange={(value) => {
                  setCustomerId(value);
                  if (value) {
                    // Tìm tên khách hàng từ ID
                    const customer = uniqueCustomers.find(c => c.id === value);
                    if (customer) {
                      // Lưu tên khách hàng nhưng chúng ta sẽ ưu tiên sử dụng ID để lọc
                      setCustomerName(customer.name);
                    }
                  } else {
                    // Đặt lại về "all" khi không chọn khách hàng
                    setCustomerName("all");
                  }
                }}
                placeholder="Chọn khách hàng"
              />
              {customerId && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    // Chỉ đặt lại giá trị mà không áp dụng bộ lọc ngay lập tức
                    setCustomerId(undefined);
                    setCustomerName("all");
                  }}
                >
                  Xóa
                </Button>
              )}
            </div>
          </div>

          <div className="grid gap-2">
            <label htmlFor="orderType" className="text-sm font-medium">
              Loại đơn hàng
            </label>
            <Select value={orderType} onValueChange={setOrderType}>
              <SelectTrigger>
                <SelectValue placeholder="Chọn loại đơn hàng" />
              </SelectTrigger>
              <SelectContent position="popper" side="bottom" align="start" sideOffset={4}>
                <SelectItem value="all">Tất cả loại</SelectItem>
                {ORDER_TYPES_FILTER.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <label className="text-sm font-medium">Khoảng thời gian</label>
            <div className="flex flex-col sm:flex-row gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full sm:w-[200px] justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "dd/MM/yyyy", { locale: vi }) : "Từ ngày"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                    locale={vi}
                  />
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full sm:w-[200px] justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "dd/MM/yyyy", { locale: vi }) : "Đến ngày"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                    locale={vi}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>
        <div className="text-xs text-muted-foreground text-center mb-2">
          Nhấn "Áp dụng" để lưu các thay đổi và áp dụng bộ lọc
        </div>
        <DialogFooter className="gap-2 sm:gap-0">
          <Button variant="outline" onClick={handleReset} className="w-full sm:w-auto">
            Đặt lại
          </Button>
          <Button onClick={handleApply} className="w-full sm:w-auto">
            Áp dụng
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}