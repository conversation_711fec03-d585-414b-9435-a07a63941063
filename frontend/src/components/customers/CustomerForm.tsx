import { useState, useEffect } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { SaveIcon, Plus, X, CreditCard, Trash2, AlertCircle, Info, Loader2, ArrowLeft, Save, Image } from "lucide-react";
import { Textarea } from "@/components/ui/textarea";
import { Customer as CustomerType, formatCurrency, parseCurrency, generateId } from "@/models/customer";
import { getCustomerById, createCustomer, updateCustomer } from "@/services/customerService";
import { getBankList } from "@/services/bankService";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { getCustomerList } from "@/services/customerService";
import apiClient from "@/lib/apiClient";
import { findCardByNumber } from "@/services/cardService";
import { CardAttachments } from "@/components/ui/card-attachment";

interface CustomerFormProps {
  customerId?: string; // Nếu có customerId thì là edit, không có thì là tạo mới
  onCancel: () => void;
  onSuccess?: () => void;
  initialCard?: {
    id: number;
    card_number: string;
    customer_id: number;
    customer_name: string;
    bank_id: number;
    is_held?: boolean;
    is_holding_card?: number | boolean;
    credit_limit: number;
    statement_date: number;
    payment_date: number;
    note: string;
    account_number?: string;
  };
}

interface Customer {
  id: string;
  name: string;
  phone: string;
  customer_fee: string;
  customer_fee_2: string;
}

interface Bank {
  id: number;
  name: string;
}

interface FormErrors {
  name?: string;
  phone?: string;
  customer_fee?: string;
  customer_fee_2?: string;
  cardNumber?: string;
  bankId?: string;
  creditLimit?: string;
}

export function CustomerForm({ customerId, onCancel, onSuccess, initialCard }: CustomerFormProps) {
  const [loading, setLoading] = useState(false);
  const [fetchingData, setFetchingData] = useState(!!customerId);

  const [errors, setErrors] = useState<FormErrors>({});

  const [customer, setCustomer] = useState<Customer>({
    id: customerId || '',
    name: "",
    phone: "",
    customer_fee: "1.80",
    customer_fee_2: "1.50"
  });

  const [cards, setCards] = useState<any[]>([]);
  const [selectedCard, setSelectedCard] = useState<any | null>(initialCard || null);

  // State cho thẻ
  // Nếu có initialCard, mặc định hiển thị tab card, ngược lại hiển thị tab customer
  const [activeTab, setActiveTab] = useState(initialCard ? "card" : "customer");

  const [cardNumber, setCardNumber] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [bankId, setBankId] = useState("");
  const [creditLimit, setCreditLimit] = useState("");
  const [statementDate, setStatementDate] = useState("");
  const [paymentDate, setPaymentDate] = useState("");
  const [cardNote, setCardNote] = useState("");
  const [banks, setBanks] = useState<Bank[]>([]);
  const [isHoldingCard, setIsHoldingCard] = useState(false);
  const [customers, setCustomers] = useState<any[]>([]);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string>("");

  // State cho modal thêm ngân hàng
  const [showAddBankModal, setShowAddBankModal] = useState(false);
  const [newBankName, setNewBankName] = useState("");
  const [addingBank, setAddingBank] = useState(false);

  // Lấy dữ liệu khách hàng khi chỉnh sửa
  useEffect(() => {
    // Verificar si hay una pestaña activa en localStorage
    const savedTab = localStorage.getItem("customerFormActiveTab");
    if (savedTab === "card") {
      setActiveTab("card");
      // Limpiar el localStorage para no mantener este estado indefinidamente
      localStorage.removeItem("customerFormActiveTab");
    }

    if (initialCard) {
      console.log("[DEBUG] Initial card received:", initialCard);
      console.log("[DEBUG] Initial card is_holding_card:", initialCard.is_holding_card, "type:", typeof initialCard.is_holding_card);
      console.log("[DEBUG] Initial card is_held:", initialCard.is_held, "type:", typeof initialCard.is_held);

      // Nếu có initialCard, tự động chọn thẻ này và không hiển thị danh sách thẻ
      setSelectedCard(initialCard);
      setCardNumber(initialCard.card_number || "");
      setBankId(initialCard.bank_id?.toString() || "");
      setCreditLimit(initialCard.credit_limit?.toString() || "");
      setStatementDate(initialCard.statement_date?.toString() || "");
      setPaymentDate(initialCard.payment_date?.toString() || "");
      setCardNote(initialCard.note || "");

      // Xử lý cả hai trường hợp is_held và is_holding_card
      if (initialCard.is_held !== undefined) {
        console.log("[DEBUG] Setting isHoldingCard from is_held:", !!initialCard.is_held);
        setIsHoldingCard(!!initialCard.is_held);
      } else if (initialCard.is_holding_card !== undefined) {
        // Xử lý nhiều kiểu dữ liệu khác nhau có thể có của is_holding_card
        const holdingCardValue = initialCard.is_holding_card === 1 ||
                               initialCard.is_holding_card === true ||
                               initialCard.is_holding_card === "1" ||
                               initialCard.is_holding_card === "true" ||
                               initialCard.is_holding_card === 1.0 ||
                               String(initialCard.is_holding_card) === "1" ||
                               String(initialCard.is_holding_card) === "true";
        console.log("[DEBUG] Setting isHoldingCard from is_holding_card:", holdingCardValue, "Original value:", initialCard.is_holding_card, "Type:", typeof initialCard.is_holding_card);
        setIsHoldingCard(holdingCardValue);
      }

      setAccountNumber(initialCard.account_number || "");
      // Auto switch to card tab
      setActiveTab("card");
    }

    if (customerId) {
      setFetchingData(true);
      fetchCustomerData();
    }
  }, [customerId, initialCard]);

  // Hàm lấy dữ liệu khách hàng và thẻ
  const fetchCustomerData = async () => {
    try {
      // Lấy thông tin khách hàng
      const customerResponse = await getCustomerById(Number(customerId));
      if (customerResponse.success && customerResponse.data) {
        const customerData = customerResponse.data;
        setCustomer({
          id: customerData.id.toString(),
          name: customerData.name || "",
          phone: customerData.phone || "",
          customer_fee: customerData.customer_fee || "1.80",
          customer_fee_2: customerData.customer_fee_2 || "1.50"
        });
      } else {
        toast.error("Không tìm thấy thông tin khách hàng");
        onCancel();
        return;
      }

      // Lấy danh sách thẻ
      if (customerId) {
        const cardsResponse = await apiClient.get(`/api/cards?customer_id=${customerId}`);
        if (cardsResponse.data && cardsResponse.data.success) {
          setCards(cardsResponse.data.data || []);
        }
      }
    } catch (error) {
      console.error("Error fetching customer:", error);
      toast.error("Đã xảy ra lỗi khi tải thông tin khách hàng");
      onCancel();
    } finally {
      setFetchingData(false);
    }
  };

  // Lấy danh sách ngân hàng
  useEffect(() => {
    const fetchBanks = async () => {
      try {
        const response = await getBankList();
        if (response.success && response.data) {
          setBanks(response.data as Bank[]);
        }
      } catch (error) {
        console.error("Error fetching banks:", error);
      }
    };

    fetchBanks();
  }, []);

  // Lấy danh sách khách hàng
  useEffect(() => {
    if (activeTab === "card") {
      fetchCustomers();
    }
  }, [activeTab]);

  // Hàm lấy danh sách khách hàng
  const fetchCustomers = async () => {
    try {
      const response = await getCustomerList();
      if (response.success) {
        setCustomers(response.data || []);
        // Nếu đang chỉnh sửa và có customerId, tự động chọn
        if (customerId) {
          setSelectedCustomerId(customerId);
        }
      } else {
        throw new Error(response.message || "Không thể tải danh sách khách hàng");
      }
    } catch (error) {
      console.error("Error fetching customers:", error);
      toast.error("Không thể tải danh sách khách hàng");
    }
  };

  // Xử lý lưu thông tin khách hàng
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Validate required fields
      if (!customer.name.trim() || !customer.phone.trim()) {
        toast.error('Vui lòng điền đầy đủ thông tin bắt buộc');
        setLoading(false);
        return;
      }

      // Kiểm tra và chuyển đổi giá trị phí
      let fee1 = 1.80, fee2 = 1.50;
      try {
        fee1 = parseFloat(customer.customer_fee);
        if (isNaN(fee1)) fee1 = 1.80;
      } catch (e) {
        fee1 = 1.80;
      }

      try {
        fee2 = parseFloat(customer.customer_fee_2);
        if (isNaN(fee2)) fee2 = 1.50;
      } catch (e) {
        fee2 = 1.50;
      }

      console.log('[DEBUG] Customer fees to update:', { customer_fee: fee1, customer_fee_2: fee2 });

      // Tạo dữ liệu khách hàng khác nhau cho tạo mới và cập nhật
      let customerData;

      if (customerId) {
        // Cập nhật khách hàng hiện có: KHÔNG gửi outstanding_fee để giữ nguyên giá trị
        customerData = {
          name: customer.name.trim(),
          phone: customer.phone.trim(),
          customer_fee: fee1,
          customer_fee_2: fee2
          // Không gửi outstanding_fee để tránh gây mất dữ liệu
        };
      } else {
        // Tạo khách hàng mới: đặt outstanding_fee = 0
        customerData = {
          name: customer.name.trim(),
          phone: customer.phone.trim(),
          customer_fee: fee1,
          customer_fee_2: fee2,
          outstanding_fee: 0,
          user_id: 1 // Gán mặc định user_id = 1
        };
      }

      if (customerId) {
        // Update existing customer
        await apiClient.put(`/api/customers/${customerId}`, customerData);
        toast.success('Cập nhật thông tin khách hàng thành công');
      } else {
        // Create new customer
        await apiClient.post('/api/customers', customerData);
        toast.success('Tạo khách hàng mới thành công');
      }

      // Reset form
      setCustomer({
        id: '',
        name: '',
        phone: '',
        customer_fee: '1.80',
        customer_fee_2: '1.50'
      });

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving customer:', error);
      toast.error('Có lỗi xảy ra khi lưu thông tin khách hàng');
    } finally {
      setLoading(false);
    }
  };

  // Xử lý lưu thông tin thẻ
  const validateCardForm = () => {
    if (!cardNumber) {
      toast.error('Vui lòng nhập số thẻ');
      return false;
    }

    if (!bankId) {
      toast.error('Vui lòng chọn ngân hàng');
      return false;
    }

    // Validate credit limit if provided
    if (creditLimit) {
      const creditLimitNum = parseFloat(creditLimit);
      if (isNaN(creditLimitNum) || creditLimitNum < 0) {
        toast.error('Hạn mức phải là số dương');
        return false;
      }
    }

    // Validate dates if provided
    if (statementDate) {
      const statementDateNum = parseInt(statementDate);
      if (isNaN(statementDateNum) || statementDateNum < 1 || statementDateNum > 31) {
        toast.error('Ngày sao kê phải từ 1-31');
        return false;
      }
    }

    if (paymentDate) {
      const paymentDateNum = parseInt(paymentDate);
      if (isNaN(paymentDateNum) || paymentDateNum < 1 || paymentDateNum > 31) {
        toast.error('Ngày thanh toán phải từ 1-31');
        return false;
      }
    }

    // Validate customer selection
    if (!customerId && !selectedCustomerId) {
      toast.error('Vui lòng chọn khách hàng');
      return false;
    }

    return true;
  };

  const handleCardSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateCardForm()) return;

    setLoading(true);

    try {
      // Check if card number exists (only when creating new or changing card number)
      if (!selectedCard?.id || (selectedCard?.id && selectedCard.card_number !== cardNumber)) {
        const existingCard = await findCardByNumber(cardNumber);
        if (existingCard.data) {
          toast.error('Số thẻ đã tồn tại trong hệ thống');
          setLoading(false);
          return;
        }
      }

      // Chuẩn bị dữ liệu cơ bản
      const cardData: any = {
        card_number: cardNumber,
        bank_id: parseInt(bankId),
        customer_id: parseInt(selectedCustomerId || customerId || '0'),
        is_holding_card: isHoldingCard ? 1 : 0
      };

      console.log("[DEBUG] Is holding card value:", isHoldingCard);
      console.log("[DEBUG] Is holding card to be sent:", cardData.is_holding_card);

      // Thêm dữ liệu tùy chọn nếu đã nhập
      if (creditLimit) {
        // Loại bỏ các ký tự không phải số trước khi chuyển đổi
        const numericValue = creditLimit.replace(/[^\d]/g, '');
        cardData.credit_limit = parseFloat(numericValue);
      }

      if (statementDate) {
        cardData.statement_date = parseInt(statementDate);
      }

      if (paymentDate) {
        cardData.payment_date = parseInt(paymentDate);
      }

      // Luôn gửi giá trị note, kể cả khi là chuỗi rỗng
      // Điều này đảm bảo khi người dùng xóa nội dung ghi chú, giá trị sẽ được cập nhật
      cardData.note = cardNote;

      // Luôn gửi giá trị account_number, kể cả khi là chuỗi rỗng
      cardData.account_number = accountNumber;

      let response;
      if (selectedCard?.id) {
        // Update existing card
        try {
          console.log('[DEBUG] Updating card with ID:', selectedCard.id);
          console.log('[DEBUG] Card data to update:', JSON.stringify(cardData, null, 2));
          console.log('[DEBUG] Card note value:', cardNote, 'length:', cardNote.length, 'type:', typeof cardNote);
          response = await apiClient.put(`/api/cards/${selectedCard.id}`, cardData);
          console.log('[DEBUG] Update response:', response);

          if (response.data && response.data.success) {
            toast.success('Cập nhật thông tin thẻ thành công');
            // Fetch lại danh sách thẻ
            if (customerId) {
              const cardsResponse = await apiClient.get(`/api/cards?customer_id=${customerId}`);
              if (cardsResponse.data && cardsResponse.data.success) {
                setCards(cardsResponse.data.data);
              }
            }

            // Luôn gọi onSuccess nếu cập nhật thành công và có initialCard
            if (initialCard && onSuccess) {
              console.log('[DEBUG] Calling onSuccess callback after update');
              onSuccess();
              return; // Thoát sớm để tránh reset form
            }

            // Reset form nếu không phải từ modal
            setCardNumber('');
            setBankId('');
            setCreditLimit('');
            setStatementDate('');
            setPaymentDate('');
            setCardNote('');
            setIsHoldingCard(false);
            setAccountNumber('');
            setSelectedCard(null);
          } else {
            toast.error((response.data && response.data.message) || 'Có lỗi xảy ra khi cập nhật thẻ');
          }
        } catch (error) {
          console.error('[DEBUG] Error updating card:', error);
          toast.error('Không thể kết nối đến máy chủ');
        }
      } else {
        // Create new card
        try {
          console.log('[DEBUG] Creating new card');
          console.log('[DEBUG] Card data to create:', JSON.stringify(cardData, null, 2));
          response = await apiClient.post('/api/cards', cardData);
          console.log('[DEBUG] Create response:', response);

          if (response.data && response.data.success) {
            toast.success('Tạo thẻ mới thành công');
            // Fetch lại danh sách thẻ
            if (customerId) {
              const cardsResponse = await apiClient.get(`/api/cards?customer_id=${customerId}`);
              if (cardsResponse.data && cardsResponse.data.success) {
                setCards(cardsResponse.data.data);
              }
            }

            // Reset form
            setCardNumber('');
            setBankId('');
            setCreditLimit('');
            setStatementDate('');
            setPaymentDate('');
            setCardNote('');
            setIsHoldingCard(false);
            setAccountNumber('');
            setSelectedCard(null);

            // Call onSuccess callback if provided
            if (onSuccess) {
              console.log('[DEBUG] Calling onSuccess callback');
              onSuccess();
            }
          } else {
            toast.error((response.data && response.data.message) || 'Có lỗi xảy ra khi tạo thẻ mới');
          }
        } catch (error) {
          console.error('[DEBUG] Error creating card:', error);
          toast.error('Không thể kết nối đến máy chủ');
        }
      }
    } catch (error) {
      console.error('Error saving card:', error);
      toast.error('Có lỗi xảy ra khi lưu thông tin thẻ');
    } finally {
      setLoading(false);
    }
  };

  // Xử lý thêm ngân hàng mới
  const handleAddBank = async () => {
    if (!newBankName.trim()) {
      toast.error("Vui lòng nhập tên ngân hàng");
      return;
    }

    try {
      setAddingBank(true);
      const response = await apiClient.post('/api/banks', { name: newBankName.trim() });

      if (response.data.success) {
        toast.success("Đã thêm ngân hàng mới thành công");

        // Thêm ngân hàng mới vào danh sách
        const newBank: Bank = response.data.data;
        setBanks(prevBanks => [...prevBanks, newBank]);

        // Tự động chọn ngân hàng mới
        setBankId(newBank.id.toString());

        // Đóng modal
        setShowAddBankModal(false);
        setNewBankName("");
      } else {
        toast.error(response.data.message || "Không thể thêm ngân hàng");
      }
    } catch (error) {
      console.error("Error adding bank:", error);
      toast.error("Đã xảy ra lỗi khi thêm ngân hàng");
    } finally {
      setAddingBank(false);
    }
  };

  // Hiển thị loading khi đang tải dữ liệu
  if (fetchingData) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center h-40">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl font-bold">
                {customerId ? "Cập nhật khách hàng" : "Tạo mới khách hàng"}
              </CardTitle>
              <CardDescription>
                {customerId ? "Cập nhật thông tin khách hàng hiện có" : "Thêm khách hàng mới vào hệ thống"}
              </CardDescription>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-2 w-full mb-6">
              <TabsTrigger value="customer">Khách hàng</TabsTrigger>
              <TabsTrigger value="card">Thẻ ngân hàng</TabsTrigger>
            </TabsList>

            <TabsContent value="customer" className="space-y-4 mt-0">
              <form onSubmit={handleSubmit}>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Tên khách hàng <span className="text-red-500">*</span></Label>
                      <Input
                        id="name"
                        placeholder="Nhập tên khách hàng"
                        value={customer.name}
                        onChange={(e) => {
                          setCustomer({ ...customer, name: e.target.value });
                          if (errors.name) {
                            setErrors({ ...errors, name: undefined });
                          }
                        }}
                        required
                      />
                      {errors.name && (
                        <div className="text-sm text-red-500 flex items-center gap-1">
                          <AlertCircle className="h-4 w-4" />
                          {errors.name}
                        </div>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="phone">Số điện thoại <span className="text-red-500">*</span></Label>
                      <Input
                        id="phone"
                        placeholder="Nhập số điện thoại"
                        value={customer.phone}
                        onChange={(e) => {
                          setCustomer({ ...customer, phone: e.target.value });
                          if (errors.phone) {
                            setErrors({ ...errors, phone: undefined });
                          }
                        }}
                        required
                      />
                      {errors.phone && (
                        <div className="text-sm text-red-500 flex items-center gap-1">
                          <AlertCircle className="h-4 w-4" />
                          {errors.phone}
                        </div>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="customer_fee">Phí thẻ quốc tế mặc định <span className="text-red-500">*</span></Label>
                      <Input
                        id="customer_fee"
                        placeholder="Nhập phí thẻ quốc tế (1.80)"
                        value={customer.customer_fee}
                        onChange={(e) => {
                          const value = e.target.value;
                          // Chấp nhận nhập mọi giá trị, không cố gắng format khi đang nhập
                          setCustomer({ ...customer, customer_fee: value });
                          if (errors.customer_fee) {
                            setErrors({ ...errors, customer_fee: undefined });
                          }
                        }}
                        required
                      />
                      {errors.customer_fee && (
                        <div className="text-sm text-red-500 flex items-center gap-1">
                          <AlertCircle className="h-4 w-4" />
                          {errors.customer_fee}
                        </div>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="customer_fee_2">Phí thẻ nội địa mặc định <span className="text-red-500">*</span></Label>
                      <Input
                        id="customer_fee_2"
                        placeholder="Nhập phí thẻ nội địa (1.50)"
                        value={customer.customer_fee_2}
                        onChange={(e) => {
                          const value = e.target.value;
                          // Chấp nhận nhập mọi giá trị, không cố gắng format khi đang nhập
                          setCustomer({ ...customer, customer_fee_2: value });
                          if (errors.customer_fee_2) {
                            setErrors({ ...errors, customer_fee_2: undefined });
                          }
                        }}
                        required
                      />
                      {errors.customer_fee_2 && (
                        <div className="text-sm text-red-500 flex items-center gap-1">
                          <AlertCircle className="h-4 w-4" />
                          {errors.customer_fee_2}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-between gap-2 pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={onCancel}
                    >
                      Hủy
                    </Button>
                    <Button
                      type="submit"
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Đang lưu...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          Lưu thông tin
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </form>
            </TabsContent>

            <TabsContent value="card" className="space-y-4 mt-0">
              {/* Hiển thị danh sách thẻ hiện có - chỉ khi không có initialCard */}
              {customerId && !initialCard && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-4">Danh sách thẻ</h3>
                  <p className="text-sm text-muted-foreground mb-2">Chọn một thẻ từ danh sách dưới đây để chỉnh sửa thông tin thẻ đó</p>
                  <div className="space-y-4">
                    {cards.map((card) => (
                      <Card
                        key={card.id}
                        className={`p-4 cursor-pointer transition-all ${selectedCard?.id === card.id ? 'border-primary border-2' : 'border hover:border-primary'}`}
                        onClick={() => {
                          // Khi click vào thẻ, cập nhật thông tin vào form
                          setSelectedCard(card);
                          setCardNumber(card.card_number || "");
                          setBankId(card.bank_id?.toString() || "");
                          setCreditLimit(card.credit_limit?.toString() || "");
                          setStatementDate(card.statement_date?.toString() || "");
                          setPaymentDate(card.payment_date?.toString() || "");
                          setCardNote(card.note || "");
                          setIsHoldingCard(card.is_held || false);
                          setAccountNumber(card.account_number || "");
                        }}
                      >
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium">{card.card_number}</h4>
                            <p className="text-sm text-gray-500">{card.bank_name}</p>
                            {card.account_number && (
                              <p className="text-sm text-gray-500">STK: {card.account_number}</p>
                            )}
                          </div>
                          <div className="text-right">
                            {card.credit_limit > 0 && (
                              <p className="text-sm font-medium">Hạn mức: {formatCurrency(card.credit_limit)}</p>
                            )}
                            {card.statement_date && (
                              <p className="text-sm text-gray-500">Sao kê: Ngày {card.statement_date}</p>
                            )}
                            {card.payment_date && (
                              <p className="text-sm text-gray-500">Thanh toán: Ngày {card.payment_date}</p>
                            )}
                          </div>
                        </div>
                        {selectedCard?.id === card.id && (
                          <div className="mt-2 text-xs text-primary font-medium">Đang chỉnh sửa thẻ này</div>
                        )}
                      </Card>
                    ))}
                  </div>
                </div>
              )}
              <form onSubmit={handleCardSubmit}>
                <div className="space-y-4">
                  {selectedCard ? (
                    <div className="bg-primary/10 p-3 rounded-md mb-4 flex items-center">
                      <Info className="h-5 w-5 text-primary mr-2" />
                      <p className="text-sm">Đang chỉnh sửa thẻ <span className="font-medium">{selectedCard.card_number}</span></p>
                    </div>
                  ) : (
                    <div className="bg-muted p-3 rounded-md mb-4 flex items-center">
                      <Plus className="h-5 w-5 text-muted-foreground mr-2" />
                      <p className="text-sm">Đang tạo thẻ mới</p>
                    </div>
                  )}
                  {/* Chỉ hiển thị dropdown chọn khách hàng khi tạo mới (không có customerId) */}
                  {!customerId && (
                    <div className="space-y-2">
                      <Label htmlFor="customerId">Khách hàng <span className="text-red-500">*</span></Label>
                      <Select value={selectedCustomerId} onValueChange={setSelectedCustomerId}>
                        <SelectTrigger>
                          <SelectValue placeholder="Chọn khách hàng" />
                        </SelectTrigger>
                        <SelectContent>
                          {customers.map((customer) => (
                            <SelectItem
                              key={customer.id}
                              value={customer.id.toString()}
                            >
                              {customer.name} - {customer.phone}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="cardNumber">Số thẻ <span className="text-red-500">*</span></Label>
                    <Input
                      id="cardNumber"
                      placeholder="1234-1234-Nguyen Van A"
                      value={cardNumber}
                      onChange={(e) => setCardNumber(e.target.value)}
                      required
                    />
                    {errors.cardNumber && (
                      <div className="text-sm text-red-500 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.cardNumber}
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="bankId">Ngân hàng <span className="text-red-500">*</span></Label>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setShowAddBankModal(true)}
                        className="text-xs px-2 h-7"
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        Thêm ngân hàng
                      </Button>
                    </div>
                    <Select value={bankId} onValueChange={setBankId}>
                      <SelectTrigger>
                        <SelectValue placeholder="Chọn ngân hàng" />
                      </SelectTrigger>
                      <SelectContent side="bottom" align="start">
                        {banks.map((bank) => (
                          <SelectItem
                            key={bank.id}
                            value={bank.id.toString()}
                          >
                            {bank.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.bankId && (
                      <div className="text-sm text-red-500 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.bankId}
                      </div>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="accountNumber">Số tài khoản</Label>
                    <Input
                      id="accountNumber"
                      placeholder="Nhập số tài khoản (nếu có)"
                      value={accountNumber}
                      onChange={(e) => setAccountNumber(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="creditLimit">Hạn mức thẻ</Label>
                    <Input
                      id="creditLimit"
                      placeholder="Nhập hạn mức thẻ"
                      value={creditLimit}
                      onChange={(e) => {
                        // Chỉ giữ lại các ký tự số
                        const numericValue = e.target.value.replace(/[^\d]/g, '');
                        // Định dạng số với dấu chấm phân cách hàng nghìn
                        if (numericValue) {
                          const formattedValue = new Intl.NumberFormat('vi-VN').format(parseInt(numericValue));
                          setCreditLimit(formattedValue);
                        } else {
                          setCreditLimit('');
                        }
                      }}
                      type="text"
                    />
                    {errors.creditLimit && (
                      <div className="text-sm text-red-500 flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        {errors.creditLimit}
                      </div>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="statementDate">Ngày sao kê</Label>
                      <Input
                        id="statementDate"
                        placeholder="Ngày trong tháng, VD: 15"
                        value={statementDate}
                        onChange={(e) => setStatementDate(e.target.value.replace(/[^\d]/g, ''))}
                        type="number"
                        min="1"
                        max="31"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="paymentDate">Ngày thanh toán</Label>
                      <Input
                        id="paymentDate"
                        placeholder="Ngày trong tháng, VD: 5"
                        value={paymentDate}
                        onChange={(e) => setPaymentDate(e.target.value.replace(/[^\d]/g, ''))}
                        type="number"
                        min="1"
                        max="31"
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 pt-2">
                    <Checkbox
                      id="isHoldingCard"
                      checked={isHoldingCard}
                      onCheckedChange={(checked) => setIsHoldingCard(checked === true)}
                    />
                    <Label htmlFor="isHoldingCard" className="cursor-pointer">
                      Đang giữ thẻ của khách hàng
                    </Label>
                  </div>

                  <div className="grid gap-3">
                    <Label htmlFor="cardNote">Ghi chú thẻ</Label>
                    <Textarea
                      id="cardNote"
                      rows={3}
                      value={cardNote}
                      onChange={(e) => setCardNote(e.target.value)}
                      placeholder="Nhập ghi chú về thẻ (nếu có)"
                    />
                  </div>

                  {/* Card Attachments section - Show for both new and existing cards */}
                  <div className="grid gap-3 mt-6">
                    <h3 className="text-lg font-semibold">Ảnh đính kèm thẻ</h3>
                    {selectedCard && selectedCard.id ? (
                      <CardAttachments cardId={selectedCard.id} />
                    ) : (
                      <div className="text-center py-6 border rounded-md bg-muted/30">
                        <Image className="h-8 w-8 mx-auto text-muted-foreground opacity-50" />
                        <p className="mt-2 text-sm text-muted-foreground">
                          Bạn cần lưu thông tin thẻ trước khi có thể đính kèm ảnh
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="flex justify-between gap-2 pt-4">
                    <div></div> {/* Espacio vacío para mantener la alineación */}
                    <div>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={onCancel}
                        className="mr-2"
                      >
                        Hủy
                      </Button>
                      <Button
                        type="submit"
                        disabled={loading}
                      >
                        {loading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Đang lưu...
                          </>
                        ) : (
                          <>
                            <CreditCard className="mr-2 h-4 w-4" />
                            {selectedCard ? 'Cập nhật thẻ' : 'Tạo thẻ'}
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </div>
              </form>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Modal thêm ngân hàng mới */}
      <Dialog open={showAddBankModal} onOpenChange={setShowAddBankModal}>
        <DialogContent className="sm:max-w-[425px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Thêm ngân hàng mới</DialogTitle>
            <DialogDescription>
              Nhập thông tin ngân hàng mới để thêm vào hệ thống
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="bankName">Tên ngân hàng <span className="text-red-500">*</span></Label>
              <Input
                id="bankName"
                value={newBankName}
                onChange={(e) => setNewBankName(e.target.value)}
                placeholder="Nhập tên ngân hàng"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAddBankModal(false)}>
              Hủy
            </Button>
            <Button onClick={() => handleAddBank()} disabled={addingBank}>
              {addingBank ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Đang thêm...
                </>
              ) : (
                "Thêm ngân hàng"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
