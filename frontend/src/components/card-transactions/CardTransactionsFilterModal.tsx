import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { CalendarIcon } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { POS } from "@/services/posService";

interface CardTransactionsFilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (filters: {
    posId: string;
    startDate: Date | undefined;
    endDate: Date | undefined;
  }) => void;
  initialFilters?: {
    posId: string;
    startDate: Date | undefined;
    endDate: Date | undefined;
  };
  posList: POS[];
}

export function CardTransactionsFilterModal({
  isOpen,
  onClose,
  onApply,
  initialFilters = {
    posId: "all",
    startDate: undefined,
    endDate: undefined,
  },
  posList,
}: CardTransactionsFilterModalProps) {
  const [posId, setPosId] = useState<string>(initialFilters.posId);
  const [startDate, setStartDate] = useState<Date | undefined>(initialFilters.startDate);
  const [endDate, setEndDate] = useState<Date | undefined>(initialFilters.endDate);

  // Thêm state để quản lý việc mở/đóng popover
  const [startDateOpen, setStartDateOpen] = useState(false);
  const [endDateOpen, setEndDateOpen] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setPosId(initialFilters.posId);
      setStartDate(initialFilters.startDate);
      setEndDate(initialFilters.endDate);
    }
  }, [isOpen, initialFilters]);

  const handleApply = () => {
    // Đảm bảo rằng ngày kết thúc là cuối ngày để bao gồm cả ngày đó
    let adjustedEndDate = endDate;
    if (endDate) {
      adjustedEndDate = new Date(endDate);
      adjustedEndDate.setHours(23, 59, 59, 999);
    }

    // Đóng các popover
    setStartDateOpen(false);
    setEndDateOpen(false);

    onApply({
      posId,
      startDate,
      endDate: adjustedEndDate,
    });
    onClose();
  };

  const handleReset = () => {
    setPosId("all");
    setStartDate(undefined);
    setEndDate(undefined);
    setStartDateOpen(false);
    setEndDateOpen(false);

    // Áp dụng bộ lọc đã reset
    onApply({
      posId: "all",
      startDate: undefined,
      endDate: undefined,
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Bộ lọc giao dịch quẹt thẻ</DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="grid gap-2">
            <label htmlFor="posId" className="text-sm font-medium">
              Máy POS
            </label>
            <Select value={posId} onValueChange={setPosId}>
              <SelectTrigger id="posId">
                <SelectValue placeholder="Chọn máy POS" />
              </SelectTrigger>
              <SelectContent side="bottom" align="start">
                <SelectItem value="all">Tất cả máy POS</SelectItem>
                {posList.map((pos) => (
                  <SelectItem key={pos.id} value={pos.id.toString()}>
                    {pos.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <label className="text-sm font-medium">Khoảng thời gian</label>
            <div className="flex flex-col sm:flex-row gap-2">
              <Popover open={startDateOpen} onOpenChange={setStartDateOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full sm:w-[200px] justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "dd/MM/yyyy", { locale: vi }) : "Từ ngày"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={(date) => {
                      setStartDate(date);
                      // Giữ popover mở sau khi chọn ngày
                      if (date) {
                        // Không đóng popover
                        setTimeout(() => setStartDateOpen(true), 0);
                      }
                    }}
                    initialFocus
                    locale={vi}
                    fromMonth={new Date(new Date().getFullYear(), 0)} // Từ tháng 1 của năm hiện tại
                    toMonth={new Date(new Date().getFullYear(), 11)} // Đến tháng 12 của năm hiện tại
                  />
                </PopoverContent>
              </Popover>

              <Popover open={endDateOpen} onOpenChange={setEndDateOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full sm:w-[200px] justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "dd/MM/yyyy", { locale: vi }) : "Đến ngày"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => {
                      setEndDate(date);
                      // Giữ popover mở sau khi chọn ngày
                      if (date) {
                        // Không đóng popover
                        setTimeout(() => setEndDateOpen(true), 0);
                      }
                    }}
                    initialFocus
                    locale={vi}
                    fromMonth={new Date(new Date().getFullYear(), 0)} // Từ tháng 1 của năm hiện tại
                    toMonth={new Date(new Date().getFullYear(), 11)} // Đến tháng 12 của năm hiện tại
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleReset}>
            Đặt lại
          </Button>
          <Button onClick={handleApply}>Áp dụng</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
