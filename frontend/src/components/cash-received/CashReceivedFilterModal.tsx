import { useState, useEffect } from "react";
import { POS } from "@/services/posService";
import { Customer } from "@/services/customerService";
import { CustomerCombobox } from "@/components/customers/CustomerCombobox";
import { Button } from "@/components/ui/button";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { cn } from "@/lib/utils";
import { CalendarIcon } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface CashReceivedFilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  onApply: (filters: {
    type: "all" | "pos" | "customer";
    posId?: number;
    customerId?: number;
    startDate: Date | undefined;
    endDate: Date | undefined;
  }) => void;
  initialFilters?: {
    type: "all" | "pos" | "customer";
    posId?: number;
    customerId?: number;
    startDate: Date | undefined;
    endDate: Date | undefined;
  };
  posList: POS[];
  customerList: Customer[];
}

export function CashReceivedFilterModal({
  isOpen,
  onClose,
  onApply,
  initialFilters = {
    type: "all",
    posId: undefined,
    customerId: undefined,
    startDate: undefined,
    endDate: undefined,
  },
  posList,
  customerList,
}: CashReceivedFilterModalProps) {
  const [type, setType] = useState<"all" | "pos" | "customer">(initialFilters.type);
  const [posId, setPosId] = useState<number | undefined>(initialFilters.posId);
  const [customerId, setCustomerId] = useState<number | undefined>(initialFilters.customerId);
  const [startDate, setStartDate] = useState<Date | undefined>(initialFilters.startDate);
  const [endDate, setEndDate] = useState<Date | undefined>(initialFilters.endDate);

  useEffect(() => {
    if (isOpen) {
      setType(initialFilters.type);
      setPosId(initialFilters.posId);
      setCustomerId(initialFilters.customerId);
      setStartDate(initialFilters.startDate);
      setEndDate(initialFilters.endDate);
    }
  }, [isOpen, initialFilters]);

  const handleApply = () => {
    const filters = {
      type,
      posId: type === 'pos' ? posId : undefined,
      customerId: type === 'customer' ? customerId : undefined,
      startDate,
      endDate,
    };

    console.log('Applying filters from modal:', filters);
    console.log('startDate:', startDate ? startDate.toISOString() : 'undefined');
    console.log('endDate:', endDate ? endDate.toISOString() : 'undefined');

    onApply(filters);
    onClose();
  };

  const handleReset = () => {
    setType("all");
    setPosId(undefined);
    setCustomerId(undefined);
    setStartDate(undefined);
    setEndDate(undefined);
  };

  // Xử lý khi thay đổi loại lọc
  const handleTypeChange = (value: string) => {
    setType(value as "all" | "pos" | "customer");
    // Reset các giá trị khác khi thay đổi loại
    setPosId(undefined);
    setCustomerId(undefined);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Bộ lọc phiếu thu</DialogTitle>
        </DialogHeader>
        <div className="grid gap-6 py-4">
          <div className="grid gap-2">
            <label htmlFor="type" className="text-sm font-medium">
              Loại phiếu thu
            </label>
            <Select value={type} onValueChange={handleTypeChange}>
              <SelectTrigger id="type">
                <SelectValue placeholder="Chọn loại phiếu thu" />
              </SelectTrigger>
              <SelectContent position="popper" side="bottom" align="start" sideOffset={4}>
                <SelectItem value="all">Tất cả</SelectItem>
                <SelectItem value="pos">POS</SelectItem>
                <SelectItem value="customer">Khách hàng</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Hiển thị danh sách POS khi chọn loại POS */}
          {type === 'pos' && (
            <div className="grid gap-2">
              <label htmlFor="posId" className="text-sm font-medium">
                Chọn POS
              </label>
              <Select value={posId?.toString() || ""} onValueChange={(value) => {
                setPosId(value ? parseInt(value) : undefined);
              }}>
                <SelectTrigger id="posId">
                  <SelectValue placeholder="Chọn POS" />
                </SelectTrigger>
                <SelectContent position="popper" side="bottom" align="start" sideOffset={4}>
                  {posList.map((pos) => (
                    <SelectItem key={pos.id} value={pos.id.toString()}>
                      {pos.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Hiển thị danh sách Khách hàng khi chọn loại Khách hàng */}
          {type === 'customer' && (
            <div className="grid gap-2">
              <label htmlFor="customerId" className="text-sm font-medium">
                Chọn Khách hàng
              </label>
              <CustomerCombobox
                value={customerId}
                onChange={(value) => setCustomerId(value)}
                placeholder="Chọn khách hàng"
              />
            </div>
          )}

          <div className="grid gap-2">
            <label className="text-sm font-medium">Khoảng thời gian</label>
            <div className="flex flex-col sm:flex-row gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full sm:w-[200px] justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "dd/MM/yyyy", { locale: vi }) : "Từ ngày"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={setStartDate}
                    initialFocus
                    locale={vi}
                  />
                </PopoverContent>
              </Popover>

              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full sm:w-[200px] justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "dd/MM/yyyy", { locale: vi }) : "Đến ngày"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={setEndDate}
                    initialFocus
                    locale={vi}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleReset}>
            Đặt lại
          </Button>
          <Button onClick={handleApply}>Áp dụng</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
